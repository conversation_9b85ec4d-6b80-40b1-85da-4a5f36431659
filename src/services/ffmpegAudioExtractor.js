/**
 * FFmpeg音频提取服务
 * 使用FFmpeg.wasm在浏览器中提取视频音频
 */

class FFmpegAudioExtractor {
  constructor() {
    this.ffmpeg = null
    this.isLoaded = false
    this.isLoading = false
  }

  /**
   * 初始化FFmpeg
   */
  async initialize(onProgress = () => {}) {
    if (this.isLoaded) return true;

    if (this.isLoading) {
      while (this.isLoading) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      return this.isLoaded;
    }

    try {
      this.isLoading = true;
      console.log('🚀 开始初始化FFmpeg...');
      onProgress({ stage: 'loading', message: '正在加载FFmpeg模块...' });

      // 动态导入FFmpeg模块
      const { FFmpeg } = await import('@ffmpeg/ffmpeg');
      const { toBlobURL } = await import('@ffmpeg/util');

      // 创建FFmpeg实例
      this.ffmpeg = new FFmpeg();

      // 设置日志处理 - 过滤不必要的调试信息
      this.ffmpeg.on('log', ({ type, message }) => {
        // 过滤掉AVIndex等调试信息，只显示重要的日志
        if (this.shouldLogMessage(type, message)) {
          console.log('[FFmpeg]', message);
        }
      });

      // 设置进度处理
      this.ffmpeg.on('progress', ({ progress }) => {
        if (progress > 0) {
          onProgress({
            stage: 'processing',
            progress: progress * 100,
            message: `处理进度: ${(progress * 100).toFixed(1)}%`
          });
        }
      });

      onProgress({ stage: 'loading', message: '正在加载FFmpeg核心文件...' });

      // 使用本地FFmpeg文件
      const baseURL = '/ffmpeg';

      await this.ffmpeg.load({
        coreURL: await toBlobURL(`${baseURL}/ffmpeg-core.js`, 'text/javascript'),
        wasmURL: await toBlobURL(`${baseURL}/ffmpeg-core.wasm`, 'application/wasm'),
      });

      this.isLoaded = true;
      this.isLoading = false;

      onProgress({ stage: 'ready', message: 'FFmpeg初始化完成' });
      console.log('✅ FFmpeg初始化成功');

      return true;

    } catch (error) {
      this.isLoading = false;
      this.isLoaded = false;
      console.error('❌ FFmpeg初始化失败:', error);
      onProgress({ stage: 'error', message: `初始化失败: ${error.message}` });
      throw error;
    }
  }

  /**
   * 从视频文件提取音频
   */
  async extractAudio(videoFile, options = {}) {
    const {
      format = 'wav',
      sampleRate = 16000,
      channels = 1,
      startTime = 0,
      duration = null,
      onProgress = () => {}
    } = options

    // 确保FFmpeg已初始化
    if (!this.isLoaded) {
      await this.initialize(onProgress)
    }

    try {
      console.log('🎵 开始音频提取')
      console.log(`📁 输入文件: ${videoFile.name} (${(videoFile.size/1024/1024).toFixed(2)}MB)`)
      console.log(`📁 文件类型: ${videoFile.type}`)

      // 验证文件
      if (!videoFile || videoFile.size === 0) {
        throw new Error('无效的视频文件：文件为空或不存在')
      }

      if (videoFile.size > 500 * 1024 * 1024) { // 500MB限制
        throw new Error('文件过大：请选择小于500MB的视频文件')
      }

      // 调试文件信息
      await this.debugFileInfo(videoFile)

      onProgress({ stage: 'preparing', message: '准备文件...' })

      // 生成文件名 - 使用更安全的文件名
      const fileExt = this.getFileExtension(videoFile.name)
      const inputName = `input_${Date.now()}.${fileExt}`
      const outputName = `output_${Date.now()}.${format}`

      console.log(`📝 输入文件名: ${inputName}`)
      console.log(`📝 输出文件名: ${outputName}`)

      // 写入输入文件 - 添加错误处理
      try {
        const { fetchFile } = await import('@ffmpeg/util')
        console.log('📥 开始写入输入文件...')

        // 将文件转换为Uint8Array
        const fileData = await fetchFile(videoFile)
        console.log(`📊 文件数据大小: ${fileData.length} bytes`)

        await this.ffmpeg.writeFile(inputName, fileData)
        console.log('✅ 输入文件写入完成')

        // 验证文件是否写入成功
        const fileList = await this.ffmpeg.listDir('/')
        console.log('📂 FFmpeg文件系统:', fileList)

        const writtenFile = fileList.find(f => f.name === inputName)
        if (!writtenFile) {
          throw new Error('文件写入失败：在FFmpeg文件系统中找不到输入文件')
        }
        console.log(`✅ 文件验证成功: ${writtenFile.name} (${writtenFile.size} bytes)`)

      } catch (writeError) {
        console.error('❌ 文件写入失败:', writeError)
        throw new Error(`文件写入失败: ${writeError.message}`)
      }

      // 构建FFmpeg命令 - 添加更多兼容性选项
      const args = ['-i', inputName]

      // 添加输入格式检测
      args.push('-f', this.getInputFormat(fileExt))

      // 添加开始时间
      if (startTime > 0) {
        args.push('-ss', startTime.toString())
      }

      // 添加持续时间
      if (duration !== null) {
        args.push('-t', duration.toString())
      }

      // 音频处理参数 - 添加更多兼容性选项
      args.push(
        '-vn',                              // 不处理视频
        '-acodec', this.getAudioCodec(format), // 音频编码器
        '-ar', sampleRate.toString(),       // 采样率
        '-ac', channels.toString(),         // 声道数
        '-avoid_negative_ts', 'make_zero',  // 避免负时间戳
        '-fflags', '+genpts',               // 生成时间戳
        '-y',                               // 覆盖输出文件
        outputName
      )

      console.log('🔧 FFmpeg命令:', args.join(' '))
      onProgress({ stage: 'processing', message: '正在提取音频...' })

      // 执行FFmpeg命令 - 添加详细错误处理
      try {
        console.log('⚡ 开始执行FFmpeg命令...')
        await this.ffmpeg.exec(args)
        console.log('✅ FFmpeg命令执行完成')
      } catch (execError) {
        console.error('❌ FFmpeg命令执行失败:', execError)

        // 尝试获取更多错误信息
        try {
          const logData = await this.ffmpeg.readFile('ffmpeg.log')
          console.error('FFmpeg日志:', new TextDecoder().decode(logData))
        } catch (logError) {
          console.warn('无法读取FFmpeg日志:', logError)
        }

        // 提供更友好的错误信息
        let errorMessage = execError.message || '未知错误'
        if (errorMessage.includes('could not be read')) {
          errorMessage = '无法读取视频文件，可能是文件格式不支持或文件损坏'
        } else if (errorMessage.includes('Invalid data')) {
          errorMessage = '视频文件数据无效，请检查文件是否完整'
        } else if (errorMessage.includes('No such file')) {
          errorMessage = '找不到输入文件，文件可能未正确上传'
        }

        throw new Error(`FFmpeg处理失败: ${errorMessage}`)
      }

      onProgress({ stage: 'reading', message: '读取输出文件...' })

      // 读取输出文件
      const data = await this.ffmpeg.readFile(outputName)
      console.log(`📊 输出文件大小: ${data.length} bytes`)

      // 创建Blob
      const mimeType = this.getMimeType(format)
      const audioBlob = new Blob([data], { type: mimeType })

      // 清理临时文件
      try {
        await this.ffmpeg.deleteFile(inputName)
        await this.ffmpeg.deleteFile(outputName)
        console.log('🗑️ 临时文件清理完成')
      } catch (e) {
        console.warn('清理临时文件失败:', e)
      }

      const result = {
        success: true,
        audioBlob: audioBlob,
        format: format,
        mimeType: mimeType,
        size: audioBlob.size,
        sampleRate: sampleRate,
        channels: channels,
        duration: duration,
        originalFile: videoFile.name
      }

      console.log('✅ 音频提取完成')
      console.log(`📊 输出大小: ${(audioBlob.size/1024/1024).toFixed(2)}MB`)

      onProgress({ stage: 'complete', message: '音频提取完成' })
      return result

    } catch (error) {
      console.error('❌ 音频提取失败:', error)
      onProgress({ stage: 'error', message: `提取失败: ${error.message}` })
      throw error
    }
  }

  /**
   * 获取文件扩展名
   */
  getFileExtension(filename) {
    const ext = filename.split('.').pop().toLowerCase()
    return ext || 'mp4'
  }

  /**
   * 获取输入格式
   */
  getInputFormat(extension) {
    const formats = {
      'mp4': 'mp4',
      'avi': 'avi',
      'mov': 'mov',
      'mkv': 'matroska',
      'webm': 'webm',
      'flv': 'flv',
      'wmv': 'asf',
      '3gp': '3gp',
      'ogv': 'ogg'
    }
    return formats[extension] || 'auto'
  }

  /**
   * 获取音频编码器
   */
  getAudioCodec(format) {
    const codecs = {
      'wav': 'pcm_s16le',
      'mp3': 'libmp3lame',
      'm4a': 'aac',
      'ogg': 'libvorbis'
    }
    return codecs[format] || 'pcm_s16le'
  }

  /**
   * 获取MIME类型
   */
  getMimeType(format) {
    const mimeTypes = {
      'wav': 'audio/wav',
      'mp3': 'audio/mpeg',
      'm4a': 'audio/mp4',
      'ogg': 'audio/ogg'
    }
    return mimeTypes[format] || 'audio/wav'
  }

  /**
   * 下载音频文件
   */
  downloadAudio(audioBlob, filename, format) {
    const url = URL.createObjectURL(audioBlob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${filename}.${format}`
    a.style.display = 'none'
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)

    setTimeout(() => {
      URL.revokeObjectURL(url)
    }, 1000)

    console.log(`💾 音频文件已下载: ${filename}.${format}`)
  }

  /**
   * 调试方法：检查文件信息
   */
  async debugFileInfo(videoFile) {
    try {
      console.log('🔍 调试文件信息:')
      console.log(`- 文件名: ${videoFile.name}`)
      console.log(`- 文件大小: ${videoFile.size} bytes (${(videoFile.size/1024/1024).toFixed(2)}MB)`)
      console.log(`- 文件类型: ${videoFile.type}`)
      console.log(`- 最后修改: ${new Date(videoFile.lastModified)}`)

      // 检查文件是否可读
      const reader = new FileReader()
      const readPromise = new Promise((resolve, reject) => {
        reader.onload = () => resolve(reader.result)
        reader.onerror = () => reject(reader.error)
      })

      reader.readAsArrayBuffer(videoFile.slice(0, 1024)) // 读取前1KB
      const buffer = await readPromise
      console.log(`- 文件头部可读: ${buffer.byteLength} bytes`)

      // 检查文件头部魔数
      const uint8Array = new Uint8Array(buffer)
      const header = Array.from(uint8Array.slice(0, 8))
        .map(b => b.toString(16).padStart(2, '0'))
        .join(' ')
      console.log(`- 文件头部: ${header}`)

      return true
    } catch (error) {
      console.error('❌ 文件调试失败:', error)
      return false
    }
  }

  /**
   * 判断是否应该记录日志信息
   * 过滤掉不必要的调试信息
   */
  shouldLogMessage(type, message) {
    // 过滤掉的日志类型和内容
    const filteredPatterns = [
      /AVIndex stream \d+, sample \d+/,  // AVIndex调试信息
      /offset 0x[0-9a-f]+/,              // 偏移量信息
      /dts \d+/,                         // DTS时间戳
      /size \d+/,                        // 大小信息
      /distance \d+/,                    // 距离信息
      /keyframe [01]/,                   // 关键帧信息
      /^frame=/,                         // 帧处理信息（太频繁）
      /^size=/,                          // 大小信息（太频繁）
      /^time=/,                          // 时间信息（太频繁）
      /^bitrate=/,                       // 比特率信息（太频繁）
      /^speed=/                          // 速度信息（太频繁）
    ]

    // 如果是错误或警告，总是显示
    if (type === 'error' || type === 'warning') {
      return true
    }

    // 检查是否匹配过滤模式
    for (const pattern of filteredPatterns) {
      if (pattern.test(message)) {
        return false
      }
    }

    // 只显示重要的信息日志
    if (type === 'info') {
      const importantPatterns = [
        /Input #\d+/,                    // 输入文件信息
        /Output #\d+/,                   // 输出文件信息
        /Stream #\d+/,                   // 流信息
        /Duration:/,                     // 持续时间
        /encoder/i,                      // 编码器信息
        /configuration:/i,               // 配置信息
        /built with/i,                   // 构建信息
        /video:/i,                       // 视频信息
        /audio:/i,                       // 音频信息
        /ffmpeg version/i,               // FFmpeg版本信息
        /libav/i,                        // 库版本信息
        /Copyright/i                     // 版权信息
      ]

      return importantPatterns.some(pattern => pattern.test(message))
    }

    // 默认不显示其他类型的日志
    return false
  }

  /**
   * 检查是否支持
   */
  static isSupported() {
    return typeof WebAssembly === 'object'
  }
}

export default FFmpegAudioExtractor