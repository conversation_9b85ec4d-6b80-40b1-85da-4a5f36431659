<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单FFmpeg测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .log {
            background-color: #f8f8f8;
            border: 1px solid #ddd;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
            margin: 10px 0;
        }
        button {
            background-color: #2196F3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #1976D2; }
        button:disabled { background-color: #ccc; cursor: not-allowed; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 简单FFmpeg测试</h1>
        <p>这个测试避免了跨域问题，直接测试本地FFmpeg功能。</p>
        
        <button onclick="runSimpleTest()">运行简单测试</button>
        <button onclick="clearLog()">清空日志</button>
        
        <div class="log" id="log">等待测试开始...\n</div>
    </div>

    <script>
        const logElement = document.getElementById('log');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : '';
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
            
            const line = `[${timestamp}] ${prefix} ${message}\n`;
            logElement.innerHTML += `<span class="${className}">${line}</span>`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }
        
        function clearLog() {
            logElement.innerHTML = '';
        }
        
        async function runSimpleTest() {
            log('🚀 开始简单FFmpeg测试', 'info');
            
            try {
                // 1. 检查基础环境
                log('1️⃣ 检查基础环境...', 'info');
                
                if (typeof WebAssembly !== 'object') {
                    log('浏览器不支持WebAssembly', 'error');
                    return;
                }
                log('WebAssembly支持正常', 'success');
                
                // 2. 检查FFmpeg文件
                log('2️⃣ 检查FFmpeg文件...', 'info');
                
                const coreResponse = await fetch('/ffmpeg/ffmpeg-core.js');
                const wasmResponse = await fetch('/ffmpeg/ffmpeg-core.wasm');
                
                log(`FFmpeg JS: ${coreResponse.status} ${coreResponse.statusText}`, coreResponse.ok ? 'success' : 'error');
                log(`FFmpeg WASM: ${wasmResponse.status} ${wasmResponse.statusText}`, wasmResponse.ok ? 'success' : 'error');
                
                if (!coreResponse.ok || !wasmResponse.ok) {
                    log('FFmpeg文件不可访问', 'error');
                    return;
                }
                
                // 3. 检查协议和安全性
                log('3️⃣ 检查安全环境...', 'info');
                log(`当前协议: ${location.protocol}`, 'info');
                log(`当前域名: ${location.hostname}`, 'info');
                log(`是否安全上下文: ${window.isSecureContext}`, 'info');
                
                // 4. 尝试创建Worker（FFmpeg可能需要）
                log('4️⃣ 检查Worker支持...', 'info');
                try {
                    const worker = new Worker('data:application/javascript,console.log("Worker test")');
                    worker.terminate();
                    log('Worker支持正常', 'success');
                } catch (workerError) {
                    log(`Worker不支持: ${workerError.message}`, 'warning');
                }
                
                // 5. 检查SharedArrayBuffer（可能需要）
                log('5️⃣ 检查SharedArrayBuffer...', 'info');
                if (typeof SharedArrayBuffer !== 'undefined') {
                    log('SharedArrayBuffer支持正常', 'success');
                } else {
                    log('SharedArrayBuffer不支持（可能影响性能）', 'warning');
                }
                
                // 6. 尝试直接加载FFmpeg核心文件
                log('6️⃣ 尝试加载FFmpeg核心...', 'info');
                
                try {
                    // 创建script标签加载FFmpeg
                    const script = document.createElement('script');
                    script.src = '/ffmpeg/ffmpeg-core.js';
                    
                    const loadPromise = new Promise((resolve, reject) => {
                        script.onload = resolve;
                        script.onerror = reject;
                        setTimeout(() => reject(new Error('加载超时')), 10000);
                    });
                    
                    document.head.appendChild(script);
                    await loadPromise;
                    
                    log('FFmpeg核心文件加载成功', 'success');
                    
                    // 检查是否有FFmpeg相关的全局变量
                    if (typeof createFFmpegCore !== 'undefined') {
                        log('发现createFFmpegCore函数', 'success');
                    } else {
                        log('未发现createFFmpegCore函数', 'warning');
                    }
                    
                } catch (loadError) {
                    log(`FFmpeg核心加载失败: ${loadError.message}`, 'error');
                }
                
                // 7. 环境总结
                log('7️⃣ 环境总结...', 'info');
                
                const issues = [];
                const recommendations = [];
                
                if (location.protocol === 'http:' && location.hostname !== 'localhost') {
                    issues.push('非HTTPS环境可能导致安全限制');
                    recommendations.push('尝试使用HTTPS或localhost');
                }
                
                if (typeof SharedArrayBuffer === 'undefined') {
                    issues.push('缺少SharedArrayBuffer支持');
                    recommendations.push('启用浏览器的跨域隔离功能');
                }
                
                if (issues.length === 0) {
                    log('🎉 环境检查通过！', 'success');
                    log('建议：尝试在主应用中使用本地FFmpeg文件路径', 'info');
                } else {
                    log('⚠️ 发现潜在问题:', 'warning');
                    issues.forEach(issue => log(`- ${issue}`, 'warning'));
                    log('💡 建议:', 'info');
                    recommendations.forEach(rec => log(`- ${rec}`, 'info'));
                }
                
                // 8. 提供解决方案
                log('8️⃣ 解决方案建议...', 'info');
                log('方案1: 修改应用使用直接文件路径而不是toBlobURL', 'info');
                log('方案2: 配置开发服务器启用HTTPS', 'info');
                log('方案3: 使用服务器端FFmpeg处理', 'info');
                
            } catch (error) {
                log(`测试失败: ${error.message}`, 'error');
                console.error('详细错误:', error);
            }
        }
        
        log('简单FFmpeg测试工具已加载', 'success');
        log('点击"运行简单测试"开始诊断', 'info');
    </script>
</body>
</html>
