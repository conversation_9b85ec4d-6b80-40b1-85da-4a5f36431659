<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FFmpeg日志过滤测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            background: #f5f5f5;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .log-output {
            background: #000;
            color: #00ff00;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .file-input {
            margin: 10px 0;
            padding: 10px;
            border: 2px dashed #ccc;
            border-radius: 4px;
            text-align: center;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .progress {
            width: 100%;
            height: 20px;
            background: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-bar {
            height: 100%;
            background: #007bff;
            width: 0%;
            transition: width 0.3s ease;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .status.info { background: #d1ecf1; color: #0c5460; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.success { background: #d4edda; color: #155724; }
    </style>
</head>
<body>
    <h1>FFmpeg日志过滤测试</h1>
    
    <div class="container">
        <h2>测试说明</h2>
        <p>这个页面用于测试FFmpeg日志过滤功能。上传一个视频文件，观察控制台输出，验证是否过滤了不必要的AVIndex调试信息。</p>
        <p><strong>注意：</strong>请打开浏览器开发者工具的控制台查看日志输出。</p>
    </div>

    <div class="container">
        <h2>文件上传</h2>
        <div class="file-input">
            <input type="file" id="videoFile" accept="video/*">
            <p>选择一个视频文件进行音频提取测试</p>
        </div>
        
        <button id="extractBtn" disabled>开始音频提取</button>
        <button id="clearLogsBtn">清空日志</button>
        
        <div class="progress">
            <div class="progress-bar" id="progressBar"></div>
        </div>
        
        <div id="status" class="status info">请选择视频文件</div>
    </div>

    <div class="container">
        <h2>控制台日志输出</h2>
        <div id="logOutput" class="log-output">等待日志输出...</div>
    </div>

    <script type="module">
        import FFmpegAudioExtractor from './src/services/ffmpegAudioExtractor.js';

        const videoFileInput = document.getElementById('videoFile');
        const extractBtn = document.getElementById('extractBtn');
        const clearLogsBtn = document.getElementById('clearLogsBtn');
        const progressBar = document.getElementById('progressBar');
        const status = document.getElementById('status');
        const logOutput = document.getElementById('logOutput');

        let extractor = new FFmpegAudioExtractor();
        let logs = [];

        // 重写console.log来捕获日志
        const originalConsoleLog = console.log;
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            
            // 添加到我们的日志显示
            const logText = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
            ).join(' ');
            
            logs.push(`[${new Date().toLocaleTimeString()}] ${logText}`);
            updateLogDisplay();
        };

        function updateLogDisplay() {
            logOutput.textContent = logs.slice(-50).join('\n'); // 只显示最近50条日志
            logOutput.scrollTop = logOutput.scrollHeight;
        }

        function updateStatus(message, type = 'info') {
            status.textContent = message;
            status.className = `status ${type}`;
        }

        function updateProgress(progress) {
            progressBar.style.width = `${progress}%`;
        }

        videoFileInput.addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (file) {
                extractBtn.disabled = false;
                updateStatus(`已选择文件: ${file.name} (${(file.size/1024/1024).toFixed(2)}MB)`, 'info');
            } else {
                extractBtn.disabled = true;
                updateStatus('请选择视频文件', 'info');
            }
        });

        extractBtn.addEventListener('click', async () => {
            const file = videoFileInput.files[0];
            if (!file) return;

            extractBtn.disabled = true;
            updateProgress(0);
            updateStatus('开始音频提取...', 'info');

            try {
                console.log('🧪 开始FFmpeg日志过滤测试');
                console.log('📝 注意观察控制台，应该看不到AVIndex调试信息');

                const result = await extractor.extractAudio(file, {
                    format: 'wav',
                    duration: 10, // 只提取10秒进行测试
                    onProgress: (progress) => {
                        if (progress.progress) {
                            updateProgress(progress.progress);
                        }
                        updateStatus(progress.message, 'info');
                    }
                });

                updateStatus(`音频提取完成! 大小: ${(result.size/1024/1024).toFixed(2)}MB`, 'success');
                updateProgress(100);

                console.log('✅ 测试完成，检查上面的日志输出');
                console.log('🔍 如果没有看到"AVIndex stream"相关的日志，说明过滤功能正常工作');

            } catch (error) {
                console.error('❌ 音频提取失败:', error);
                updateStatus(`提取失败: ${error.message}`, 'error');
            } finally {
                extractBtn.disabled = false;
            }
        });

        clearLogsBtn.addEventListener('click', () => {
            logs = [];
            updateLogDisplay();
            updateStatus('日志已清空', 'info');
        });

        // 初始化
        updateLogDisplay();
        console.log('🚀 FFmpeg日志过滤测试页面已加载');
        console.log('📋 请上传视频文件进行测试');
    </script>
</body>
</html>
