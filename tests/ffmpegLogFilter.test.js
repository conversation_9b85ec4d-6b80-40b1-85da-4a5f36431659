/**
 * FFmpeg日志过滤功能测试
 */

import FFmpegAudioExtractor from '../src/services/ffmpegAudioExtractor.js'

describe('FFmpeg日志过滤测试', () => {
  let extractor

  beforeEach(() => {
    extractor = new FFmpegAudioExtractor()
  })

  describe('shouldLogMessage方法测试', () => {
    test('应该过滤AVIndex调试信息', () => {
      const messages = [
        'AVIndex stream 0, sample 4675, offset 1e3a509, dts 4679675, size 6430, distance 175, keyframe 0',
        'AVIndex stream 1, sample 1234, offset 2f4b608, dts 1234567, size 3210, distance 89, keyframe 1'
      ]

      messages.forEach(message => {
        expect(extractor.shouldLogMessage('info', message)).toBe(false)
      })
    })

    test('应该过滤频繁的处理信息', () => {
      const messages = [
        'frame= 1234 fps=30 q=28.0 size= 1024kB time=00:00:41.23 bitrate= 203.4kbits/s speed=1.2x',
        'size= 2048kB',
        'time=00:01:23.45',
        'bitrate= 128kbits/s',
        'speed=0.8x'
      ]

      messages.forEach(message => {
        expect(extractor.shouldLogMessage('info', message)).toBe(false)
      })
    })

    test('应该显示重要的输入输出信息', () => {
      const messages = [
        'Input #0, mov,mp4,m4a,3gp,3g2,mj2, from \'input.mp4\'',
        'Output #0, wav, to \'output.wav\'',
        'Stream #0:0: Video: h264 (avc1 / 0x31637661), yuv420p, 1920x1080',
        'Stream #0:1: Audio: aac (mp4a / 0x6134706D), 48000 Hz, stereo',
        'Duration: 00:02:30.45, start: 0.000000, bitrate: 2500 kb/s'
      ]

      messages.forEach(message => {
        expect(extractor.shouldLogMessage('info', message)).toBe(true)
      })
    })

    test('应该始终显示错误和警告', () => {
      const errorMessages = [
        'Invalid data found when processing input',
        'Could not find codec parameters',
        'No such file or directory'
      ]

      const warningMessages = [
        'Deprecated pixel format used',
        'Stream map \'0\' matches no streams'
      ]

      errorMessages.forEach(message => {
        expect(extractor.shouldLogMessage('error', message)).toBe(true)
      })

      warningMessages.forEach(message => {
        expect(extractor.shouldLogMessage('warning', message)).toBe(true)
      })
    })

    test('应该显示编码器和配置信息', () => {
      const messages = [
        'ffmpeg version 4.4.0 Copyright (c) 2000-2021 the FFmpeg developers',
        'built with gcc 9.3.0',
        'configuration: --enable-cross-compile --target-os=none',
        'libavutil      56. 70.100 / 56. 70.100',
        'encoder         : Lavc58.134.100 libx264'
      ]

      messages.forEach(message => {
        expect(extractor.shouldLogMessage('info', message)).toBe(true)
      })
    })

    test('应该过滤其他不重要的调试信息', () => {
      const messages = [
        'offset 0x1e3a509',
        'dts 4679675',
        'size 6430',
        'distance 175',
        'keyframe 0',
        'some random debug info'
      ]

      messages.forEach(message => {
        expect(extractor.shouldLogMessage('debug', message)).toBe(false)
      })
    })
  })

  describe('日志过滤集成测试', () => {
    test('应该正确处理混合日志类型', () => {
      const testCases = [
        { type: 'info', message: 'Input #0, mov,mp4,m4a,3gp,3g2,mj2, from \'test.mp4\'', expected: true },
        { type: 'info', message: 'AVIndex stream 0, sample 4675, offset 1e3a509', expected: false },
        { type: 'error', message: 'Invalid data found', expected: true },
        { type: 'warning', message: 'Deprecated format', expected: true },
        { type: 'info', message: 'frame= 1234 fps=30', expected: false },
        { type: 'info', message: 'Duration: 00:02:30.45', expected: true }
      ]

      testCases.forEach(({ type, message, expected }) => {
        expect(extractor.shouldLogMessage(type, message)).toBe(expected)
      })
    })
  })
})
